import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:logger/logger.dart';
import 'package:onekitty/models/events/events_model.dart';
import 'package:onekitty/screens/dashboard/pages/events/views/screens/view_single_event_viewer.dart' show BuyTicket;
import 'package:onekitty/screens/dashboard/pages/events/views/widgets/attendees_widget.dart';
import 'package:qr_flutter/qr_flutter.dart';
import 'package:onekitty/screens/dashboard/pages/events/controllers/controllers.dart';
import 'package:onekitty/screens/dashboard/pages/events/controllers/edit_event_controller.dart';
import 'package:onekitty/screens/dashboard/pages/events/controllers/view_single_event.dart';
import 'package:onekitty/models/events/media_models.dart';
import 'package:onekitty/models/events/tickets_model.dart';
import 'package:onekitty/utils/date_formatter.dart';
import 'package:onekitty/utils/image_popup.dart';
import 'package:onekitty/utils/show_cached_network_image.dart';
import 'package:onekitty/utils/timeSince.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:share_plus/share_plus.dart';
import '/utils/glassmorphic.dart';
import 'package:carousel_slider/carousel_slider.dart' as q;
import 'package:onekitty/screens/dashboard/pages/events/views/widgets/my_quill_editor.dart';
import 'package:onekitty/main.dart' show isLight;
import 'map_page.dart';

class ViewSingleEventViewer extends StatefulWidget {
  final Event? event;
  final bool isFromDeepLink;

  const ViewSingleEventViewer(
      {super.key, this.isFromDeepLink = false, this.event});

  @override
  State<ViewSingleEventViewer> createState() => _ViewSingleEventViewerState();
}

class _ViewSingleEventViewerState extends State<ViewSingleEventViewer> {
  final _refreshController = RefreshController(initialRefresh: true);
  final controller = Get.put(ViewSingleEventController());
  final getEventController = Get.put(EditEventController());
  @override
  void initState() {
    if (!widget.isFromDeepLink) {
      _onRefresh();
    }

    super.initState();
  }

  @override
  void dispose() {
    super.dispose();
  }

  void _onRefresh() async {
    try {
      final eventId = widget.event?.id ?? controller.event.value.id ?? 0;

      // Use the new refresh method that updates all related lists
      await controller.refreshEventData(eventId, isOrganizer: false);

      _refreshController.refreshCompleted();
    } catch (e) {
      _refreshController.refreshCompleted();
      Logger().e(e);
    }
  }

  // Add QR Code dialog method
  void _showQRCodeDialog(BuildContext context, String eventUrl) {
    showDialog(
      context: context,
      builder: (context) => Dialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        child: Container(
          padding: const EdgeInsets.all(20),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                'event_qr_code'.tr,
                style: TextStyle(
                  fontSize: 18.spMin,
                  fontWeight: FontWeight.bold,
                  color: primaryColor,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                'scan_to_open_event_page'.tr,
                style: TextStyle(
                  fontSize: 14.spMin,
                  color: Colors.grey.shade600,
                ),
              ),
              const SizedBox(height: 20),
              QrImageView(
                data: eventUrl,
                version: QrVersions.auto,
                size: 200.0,
                backgroundColor: Colors.white,
                gapless: false,
                errorStateBuilder: (context, error) => Center(
                  child: Text(
                    'error_generating_qr_code'.tr.replaceAll('{error}', error.toString()),
                    textAlign: TextAlign.center,
                  ),
                ),
              ),
              const SizedBox(height: 20),
              Text(
                '${'url'.tr}: $eventUrl',
                style: TextStyle(
                  fontSize: 12.spMin,
                  color: Colors.grey.shade600,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  TextButton.icon(
                    onPressed: () {
                      Share.share(eventUrl);
                      Navigator.pop(context);
                    },
                    icon: const Icon(Icons.share, color: primaryColor),
                    label: Text('share_url'.tr,
                        style: const TextStyle(color: primaryColor)),
                  ),
                  ElevatedButton(
                    onPressed: () => Navigator.pop(context),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: primaryColor,
                      foregroundColor: Colors.white,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    child: Text('close'.tr),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  final activeIndex = 0.obs;
  @override
  Widget build(BuildContext context) {
    final deepLinkEvent = widget.event;
    return WillPopScope(
      onWillPop: () async {
        if (Get.isDialogOpen ?? false) {
          Get.back();
          return false;
        }
        Get.replace("/passreqPage");
        return false;
      },
      child: ScreenUtilInit(
        splitScreenMode: true,
        designSize: const Size(392.72727272727275, 850.9090909090909),
        builder: (context, child) {
          return Scaffold(body: Obx(() {
            final event = deepLinkEvent ?? controller.event.value;
            return SmartRefresher(
              onRefresh: _onRefresh,
              controller: _refreshController,
              child: CustomScrollView(slivers: [
                SliverAppBar(
                    // floating: true,
                    leadingWidth: 88,
                    pinned: true,
                    backgroundColor: isLight.value
                        ? scaffoldBackgroundColor
                        : Colors.transparent,
                    actions: [
                      // QR Code button
                      GlassmorphicContainer(
                        onTap: () {
                          _showQRCodeDialog(context,
                              'https://www.onekitty.co.ke/events/${event.username}');
                        },
                        color: Colors.black,
                        blurRadius: 20,
                        cornerRadius: 360,
                        child: const Icon(Icons.qr_code,
                            color: Colors.white, size: 20),
                      ),
                      const SizedBox(width: 10),
                    ],
                    bottom: PreferredSize(
                        preferredSize: const Size(0, 0),
                        child: Container(
                            height: 55,
                            alignment: Alignment.center,
                            width: 280,
                            padding: const EdgeInsets.symmetric(
                                vertical: 1, horizontal: 2),
                            decoration: BoxDecoration(
                                color: isLight.value
                                    ? Colors.white
                                    : Colors.grey[900],
                                boxShadow: [
                                  BoxShadow(
                                      color: isLight.value
                                          ? Colors.grey.shade500
                                          : Colors.black,
                                      offset: const Offset(0, 2),
                                      blurRadius: 12,
                                      spreadRadius: -4)
                                ],
                                borderRadius: BorderRadius.circular(30)),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                AttendeesWidget(
                                  count: event.count ?? 0,
                                  size: 18,
                                  textSize: 15,
                                ),
                                Material(
                                  borderRadius: BorderRadius.circular(25),
                                  color: primaryColor,
                                  child: SizedBox(
                                    height: 30,
                                    width: 70,
                                    child: MaterialButton(
                                        onPressed: () {
                                          //  Share event link
                                          final url =
                                              'https://www.onekitty.co.ke/events/${event.username}'; // Replace with actual URL

                                          Share.share(
                                            'check_out_this_event'.tr.replaceAll('{url}', url),
                                            subject: event.title,
                                          );
                                        },
                                        child: Text(
                                          'invite'.tr,
                                          style: const TextStyle(
                                              color: Colors.white,
                                              fontWeight: FontWeight.w400,
                                              fontSize: 12),
                                        )),
                                  ),
                                )
                              ],
                            ))),
                    leading: GlassmorphicContainer(
                      onTap: () => Get.replace("/passreqPage"),
                      color: Colors.black,
                      blurRadius: 20,
                      cornerRadius: 24,
                      child:   Row(
                        children: [
                          const Icon(
                            Icons.arrow_back,
                            color: Colors.white,
                            size: 18,
                          ),
                          const SizedBox(width: 8),
                          Text('back'.tr, style: const TextStyle(color: Colors.white))
                        ],
                      ),
                    ),
                    expandedHeight: 250,
                    flexibleSpace: FlexibleSpaceBar(
                        background: Hero(
                      tag: 'image:${event.id}',
                      child: Stack(alignment: Alignment.topCenter, children: [
                        SizedBox(
                          height: 250.h,
                          child:
                              Stack(alignment: Alignment.topCenter, children: [
                            q.CarouselSlider.builder(
                              itemCount: event.eventMedia?.length ?? 0,
                              itemBuilder: (context, index, realIndex) {
                                final mediaUrl = event.eventMedia?[index].url;
                                return InkWell(
                                  onTap: () {
                                    Get.to(() => ImagePopup(
                                        pos: realIndex,
                                        title: event.title,
                                        imageUrl: event.eventMedia
                                                ?.map((e) => e.url??'')
                                                .toList() ??
                                            <String>[]));
                                  },
                                  child: ShowCachedNetworkImage(
                                    fit: BoxFit.cover,
                                    width: MediaQuery.sizeOf(context).width,
                                    height: 260.h,
                                    imageurl: mediaUrl ?? '',
                                  ),
                                );
                              },
                              options: q.CarouselOptions(
                                height: 260.h,
                                viewportFraction: 1.0,
                                enableInfiniteScroll: false,
                                onPageChanged: (index, reason) {
                                  activeIndex(index);
                                  // carouselController.jumpToPage(index);
                                },
                              ),
                            ),
                            (event.eventMedia ?? <EventMedia>[]).isEmpty
                                ? const SizedBox()
                                : Positioned(
                                    bottom: 38.h,
                                    left: 0,
                                    right: 0,
                                    child: Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.center,
                                      children: List.generate(
                                        event.eventMedia?.length ?? 0,
                                        (index) => Container(
                                          margin: const EdgeInsets.symmetric(
                                              horizontal: 4),
                                          width: 8,
                                          height: 8,
                                          decoration: BoxDecoration(
                                            shape: BoxShape.circle,
                                            color: activeIndex.value == index
                                                ? Colors.white70
                                                : Colors.white24,
                                          ),
                                        ),
                                      ),
                                    ),
                                  ),
                          ]),
                        ),
                      ]),
                    ))),
                SliverToBoxAdapter(
                    child: Hero(
                  tag: 'text:${event.id}',
                  child: Padding(
                    padding: const EdgeInsets.all(8.0),
                    child: Material(
                      color: Colors.transparent,
                      child: Text(event.title,
                          style: TextStyle(
                              color:
                                  isLight.value ? Colors.black : Colors.white,
                              fontWeight: FontWeight.w700,
                              fontSize: 22)),
                    ),
                  ),
                )),
                SliverToBoxAdapter(
                  child: Column(
                    children: [
                      ListTile(
                        onTap: () => Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => MapScreen(
                              viewOnly: true,
                              longitude: event.longitude,
                              latitude: event.latitude,
                            ),
                          ),
                        ),
                        leading: Container(
                            padding: const EdgeInsets.all(8),
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(10),
                              color: primaryColor.withOpacity(0.15),
                            ),
                            child: Hero(
                                tag: "ilocation${event.id}",
                                child: Image.asset(
                                    'assets/images/icons/location.png',
                                    height: 30,
                                    width: 30,
                                    color: primaryColor))),
                        title: Hero(
                          tag: "tlocation${event.id}",
                          child: Material(
                            color: Colors.transparent,
                            child: Text(
                              event.venue,
                              style:
                                  const TextStyle(fontWeight: FontWeight.w600),
                            ),
                          ),
                        ),
                        subtitle: Text(event.locationTip,
                            style: const TextStyle(
                                fontSize: 12, color: Colors.grey)),
                      ),
                      ListTile(
                        onTap: () {
                          showDialog(
                            context: context,
                            builder: (context) => Dialog(
                              child: Padding(
                                padding: const EdgeInsets.all(16.0),
                                child: Column(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    CalendarDatePicker(
                                      initialDate: event.startDate!,
                                      firstDate: DateTime(2000),
                                      lastDate: DateTime(2100),
                                      onDateChanged: (_) {},
                                      selectableDayPredicate: (DateTime date) {
                                        return date.year ==
                                                event.startDate!.year &&
                                            date.month ==
                                                event.startDate!.month &&
                                            date.day == event.startDate!.day;
                                      },
                                    ),
                                    Align(
                                      alignment: Alignment.centerRight,
                                      child: TextButton(
                                        onPressed: () => Navigator.pop(context),
                                        child: Text('close'.tr),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          );
                        },
                        leading: Container(
                            padding: const EdgeInsets.all(8),
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(10),
                              color: primaryColor.withOpacity(0.15),
                            ),
                            child: Hero(
                              tag: "idate${event.id}",
                              child: Image.asset(
                                  'assets/images/icons/calendar.png',
                                  height: 30,
                                  width: 30,
                                  color: primaryColor),
                              //  const Icon(Icons.calendar_month_rounded,
                              //     color: primaryColor)
                            )),
                        title: Hero(
                          tag: "tdate${event.id}",
                          child: Material(
                            color: Colors.transparent,
                            child: Text(
                              formatDate("${event.startDate?.toLocal()}"),
                              style:
                                  const TextStyle(fontWeight: FontWeight.w600),
                            ),
                          ),
                        ),
                        subtitle: Text(
                            event.startDate == null || event.endDate == null
                                ? ''
                                : '${DateFormat('EEEE').format(event.startDate?.toLocal() ?? DateTime.now())}, ${formatTime("${event.startDate?.toLocal()}")} - ${formatDate("${event.startDate?.toLocal()}") == formatDate("${event.endDate?.toLocal()}") ? formatTime("${event.endDate?.toLocal()}") : "${DateFormat('EEEE').format(event.endDate?.toLocal() ?? DateTime.now())}, ${formatTime("${event.endDate?.toLocal()}")}"}\n'
                                    ' - ${highPrecisiontimeSince(event.endDate?.toLocal() ?? DateTime.now())}',
                            style: const TextStyle(
                                fontSize: 12, color: Colors.grey)),
                      ),
                    ],
                  ),
                ),
                SliverToBoxAdapter(
                  child: Padding(
                    padding:
                        EdgeInsets.symmetric(horizontal: 12.0.w, vertical: 2.h),
                    child: Text(
                      'about_event'.tr,
                      style: TextStyle(
                          fontSize: 16.spMin,
                          fontWeight: FontWeight.w500,
                          color: isLight.value ? Colors.black : Colors.white70),
                    ),
                  ),
                ),
                SliverToBoxAdapter(
                    child: Padding(
                  padding:
                      EdgeInsets.symmetric(horizontal: 12.0.w, vertical: 4.h),
                  child: Hero(
                      tag: 'desc:${event.id}',
                      child: QuillEditorWidget(
                        text: event.description,
                        readMore: true,
                        tag: 'desc_${event.id}_deeplink',
                      )),
                )),
                SliverToBoxAdapter(
                  child: Padding(
                    padding:
                        EdgeInsets.symmetric(horizontal: 12.0.w, vertical: 2.h),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const SizedBox(height: 8),
                        ColoredBox(
                          color: Theme.of(context).scaffoldBackgroundColor,
                          child: ListTile(
                            title: Text(event.username,
                                style: const TextStyle(
                                    fontSize: 14, fontWeight: FontWeight.w600)),
                            subtitle: Text('organizer'.tr,
                                style: TextStyle(
                                    fontSize: 12, color: Colors.grey.shade700)),
                            leading: CircleAvatar(
                              backgroundColor: primaryColor,
                              child: Text(
                                event.title.isNotEmpty
                                    ? event.title
                                        .split(' ')
                                        .map((word) => word[0])
                                        .take(2)
                                        .join()
                                        .toUpperCase()
                                    : '',
                                style: const TextStyle(color: Colors.white),
                              ),
                            ),
                          ),
                        ),
                        const SizedBox(height: 8),
                      ],
                    ),
                  ),
                ),
                SliverToBoxAdapter(
                    child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 8.0),
                  child: Column(
                    children: [
                      Material(
                        borderRadius: BorderRadius.circular(25),
                        color: primaryColor,
                        child: SizedBox(
                          // height: 30,
                          // width: 70,
                          child: Row(
                            children: [
                              Expanded(
                                child: Builder(builder: (context) {
                                  return MaterialButton(
                                      onPressed: () async {
                                        showDialog(
                                            context: context,
                                            builder: (context) => BuyTicket(
                                                eventId: event.id??0,
                                                ticket: event.tickets ??
                                                    <Ticket>[]));
                                      },
                                      child: Text(
                                        'buy_ticket'.tr,
                                        style: const TextStyle(
                                            color: Colors.white,
                                            fontWeight: FontWeight.w400,
                                            fontSize: 16),
                                      ));
                                }),
                              ),
                            ],
                          ),
                        ),
                      )
                    ],
                  ),
                )),
                const SliverToBoxAdapter(child: SizedBox(height: 100))
              ]),
            );
          }));
        },
      ),
    );
  }
}
